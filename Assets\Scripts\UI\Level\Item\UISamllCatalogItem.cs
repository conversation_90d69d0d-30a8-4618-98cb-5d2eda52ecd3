using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class UISamllCatalogItem : UICatalogItemBase
{
    public RectTransform linkLine;
    private float lineWidth;
    public GameObject select;
    
    [Tooltip("图标选中时显示的边框图片")]
    public Image selectedBorderImage;
    
    [Toolt<PERSON>("边框图片的精灵资源")]
    public Sprite borderSprite;

    public void Awake()
    {
        // 初始化连线
        if(linkLine != null)
            linkLine.gameObject.SetActive(false);
            
        // 初始化边框图片
        InitBorderImage();
    }
    
    private void InitBorderImage()
    {
        if(selectedBorderImage != null)
        {
            // 设置边框图片精灵
            if(borderSprite != null)
            {
                selectedBorderImage.sprite = borderSprite;
            }
            
            // 初始状态下隐藏边框
            selectedBorderImage.enabled = false;
            Debug.Log("初始化边框图片完成");
        }
        else
        {
            Debug.LogWarning("边框图片组件未分配，请在Inspector中设置");
        }
    }
    // 重写SetRarity方法
    public override void SetRarity()
    {
        // 调用基类的SetRarity方法
        base.SetRarity();
        // 设置背景图片
        bg.sprite = ItemService.Instance.SmallCatalogBackground[card.rarity - 1];
    }

    public void DrawLineToTarget(RectTransform targetRect)
    {
        if (linkLine == null || targetRect == null)
        {
            if (linkLine != null)
            {
                linkLine.gameObject.SetActive(false);
            }
            Debug.LogError("连线失败");
            return;
        }
        lineWidth = linkLine.sizeDelta.x;
        linkLine.gameObject.SetActive(true); // 确保线条是可见的

        Vector3 startPointWorld = linkLine.position;
        Vector3 endPointWorld = targetRect.position;
        Vector3 direction = endPointWorld - startPointWorld;
        float distance = direction.magnitude;
        linkLine.sizeDelta = new Vector2(lineWidth, distance);
        float angle = Mathf.Atan2(direction.y, direction.x) * Mathf.Rad2Deg;
        linkLine.eulerAngles = new Vector3(0, 0, angle - 90f);

    }

    public override void InitData(CardBase card)
    {
        if (card == null) return;
        this.card = card;
        icon.sprite = card.Icon;
        iconMask.sprite = card.Icon;
        SetRarity();
        SetLockState();
        
        // 确保边框图片处于隐藏状态
        if (selectedBorderImage != null)
        {
            selectedBorderImage.enabled = false;
            Debug.Log("在InitData中重置边框图片为隐藏状态");
        }
    }

    public void Select(bool isSelect)
    {
        if (select != null)
        {
            select.SetActive(isSelect);
        }
        
        Debug.Log($"调用Select方法，isSelect={isSelect}");
        
        // 显示或隐藏选中边框图片
        if (selectedBorderImage != null)
        {
            // 直接设置Image组件的显示状态
            selectedBorderImage.enabled = isSelect;
            
            // 如果是选中状态，确保有边框图片
            if (isSelect && selectedBorderImage.sprite == null && borderSprite != null)
            {
                selectedBorderImage.sprite = borderSprite;
            }
            
            Debug.Log($"边框图片状态更新：Image组件启用={selectedBorderImage.enabled}, 有精灵={selectedBorderImage.sprite != null}");
        }
        else if (isSelect)
        {
            Debug.LogError("selectedBorderImage未分配，请在Inspector中设置");
        }
    }
    
    /// <summary>
    /// 设置选中边框图片
    /// </summary>
    /// <param name="sprite">边框图片精灵</param>
    public void SetSelectedBorder(Sprite sprite)
    {
        if (selectedBorderImage != null && sprite != null)
        {
            // 保存边框精灵资源
            this.borderSprite = sprite;
            selectedBorderImage.sprite = sprite;
            Debug.Log("成功设置边框图片精灵");
        }
    }

}
