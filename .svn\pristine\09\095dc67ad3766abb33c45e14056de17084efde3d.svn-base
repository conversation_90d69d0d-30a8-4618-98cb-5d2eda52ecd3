﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class UICatalogWeaponItem : UICatalogItemBase
{

    public override void SetRarity()
    {
        base.SetRarity();
        if (card.IsLock)
        {
            bg.sprite = ItemService.Instance.CatalogBackground[card.rarity - 1];
        }
        else
        {
            bg.sprite = ItemService.Instance.CatalogLockBackground[card.rarity - 1];
        }
    }

}
