%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 69ac6b95ef75bd74e856ed39990d2715, type: 3}
  m_Name: CW_20081_TaserGun
  m_EditorClassIdentifier: 
  ID: 0
  CardNumber: 20081
  CardTypes: 
  ItemName: Txt_CombineWeapon_Name_TaserGun
  ItemType: 
  IconPath: Image\UI\CombineWeapon\UI_wq_djq_01
  PropertyDescribe: 
  Effects: []
  UpgradeIds: 
  UpgradeDatas: []
  Remarks: 
  rarity: 2
  starLevel: 1
  Cost: 3
  weaponCard: {fileID: 11400000, guid: 7146d118bf67263469b99511ef78bb96, type: 2}
  price: 9
  ChildWeaponIds: 10053,10141
  ChildCombineWeapons: []
  ParentCombineWeapons: []
  weaponType: 32
  damageType: 1
  range: 800
  baseAttack: 320
  physicalAttackCoefficient: 0
  magicPowerCoefficient: 3.5
  hpAttackCoefficient: 0
  speedAttackCoefficient: 0
  criticalChance: 10
  criticalDamage: 80
  attackInterval: 0.2
  ProjectilePrefabPath: Prefabs\Projectiles\BulletProjectile_CiLi
  ProjectileAudioClip: Sounds\Ranged\shotgun_01,Sounds\Ranged\shotgun_02,Sounds\Ranged\shotgun_03,Sounds\Ranged\shotgun_04
  weaponName: TaserGun
