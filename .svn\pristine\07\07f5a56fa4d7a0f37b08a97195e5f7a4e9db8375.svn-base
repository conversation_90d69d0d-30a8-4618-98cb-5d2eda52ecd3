﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class AppearanceData : ScriptableObject
{
    public int CardNumber;
    public string SpritePath;
    public string Position;
    public int DisplayPriority;
    public int Depth;
    [Header("备注")]
    public string Remarks;

    private Sprite _Sprite;
    public Sprite Sprite
    {
        get
        {
            if(_Sprite == null)
            {
                _Sprite = SpritePath.GetAssestWithPath<Sprite>();
            }
            return _Sprite;
        }
        set { _Sprite = value; }
    }
    public void ClearCache()
    {
        Sprite = null;
    }
}
