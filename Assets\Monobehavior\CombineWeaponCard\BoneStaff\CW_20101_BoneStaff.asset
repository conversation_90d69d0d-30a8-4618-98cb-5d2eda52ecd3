%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 69ac6b95ef75bd74e856ed39990d2715, type: 3}
  m_Name: CW_20101_BoneStaff
  m_EditorClassIdentifier: 
  ID: 0
  CardNumber: 20101
  CardTypes: 
  ItemName: Txt_CombineWeapon_Name_BoneStaff
  ItemType: 
  IconPath: Image\UI\CombineWeapon\UI_wq_gmz01
  PropertyDescribe: 
  Effects: []
  UpgradeIds: 
  UpgradeDatas: []
  Remarks: 
  rarity: 2
  starLevel: 1
  Cost: 6
  weaponCard: {fileID: 11400000, guid: 9b606f8b076d4f14882b966158d3644e, type: 2}
  price: 0
  ChildWeaponIds: 10061,10152
  ChildCombineWeapons: []
  ParentCombineWeapons: []
  weaponType: 35
  damageType: 1
  range: 800
  baseAttack: 250
  physicalAttackCoefficient: 0
  magicPowerCoefficient: 2.8
  hpAttackCoefficient: 0
  speedAttackCoefficient: 0
  criticalChance: 8
  criticalDamage: 90
  attackInterval: 1.43
  ProjectilePrefabPath: Prefabs\Projectiles\BulletProjectileBolt
  ProjectileAudioClip: Sounds\Ranged\shotgun_01,Sounds\Ranged\shotgun_02,Sounds\Ranged\shotgun_03,Sounds\Ranged\shotgun_04
  weaponName: BoneStaff
