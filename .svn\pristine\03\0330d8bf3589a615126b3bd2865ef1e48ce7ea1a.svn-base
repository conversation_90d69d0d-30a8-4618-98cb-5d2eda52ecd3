﻿using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Random = UnityEngine.Random;

[CreateAssetMenu(fileName = "ItemService", menuName = "Singletons/Item Service")]
public class ItemService : SignletonsDataBase<ItemService>
{
    public MonsterSettings[] Elites;
    public PickupLoot PickPrefabe;
    public PickupItem PickupItem;
    public SuitCardEffect[] Sets;
    public Sprite StatMaxHpSprite;
    public Sprite BoxSprite;
    public Sprite LegendaryBoxSprite;
    public Sprite GoldsSprite;
    public Sprite DiamondSprite;
    [Header("NPC Prefabs")]
    public NPCBase ShopNPC;
    public NPCBase LevelNPC;

    [Header("武器粒子")]
    public ParticleSystem GhostBurningParticles;
    public ParticleSystem TorchBurningParticles;
    [Header("Particles")]
    public BurningParticles BurningParticles;
    [Header("Run Smoke")]
    public ParticleSystem RunSmoke;

    [Header("Level Head Background")]
    public Sprite[] LevelHeadBackground;
    [Header("Level Mask Background")]
    public Sprite[] LevelMaskBackground;

    [Header("图鉴稀有度背景")]
    public Sprite[] CatalogBackground;
    public Sprite[] CatalogLockBackground;
    public Sprite[] SmallCatalogBackground;

    [Header("锁 图片")]
    public Sprite UnlockSprite;
    public Sprite LockSprite;
    [Header("X 图片")]
    public Sprite ForkSprite;

    TiersData[] m_TiersData;



    const float CHANCE_WEAPON = 0.35f;
    const float CHANCE_SAME_WEAPON = 0.2f;
    const float CHANCE_SAME_WEAPON_CLASS = 0.35f;
    const int MAX_WAVE_TWO_WEAPONS_GUARANTEED = 3;
    const int MAX_WAVE_ONE_WEAPON_GUARANTEED = 6;
    const float BONUS_CHANCE_SAME_WEAPON_CLASS = 0.15f;
    const float CHANCE_WANTED_ITEM_TAG = 0.05f;
    public void ResetTiersData()
    {
        m_TiersData = new TiersData[4]
        {
            new TiersData(0f,1f,0f,1f),
            new TiersData(0f,0f,0.06f,0.6f),
            new TiersData(2f,0f,0.02f,0.25f),
            new TiersData(6f,0f,0.0023f,0.08f),
        };
    }

    public void InitUnlockedPool()
    {
        ResetTiersData();
        foreach (var item in GameData.Instance.AllProp)
        {
            if (!item.IsLock && !item.CardTypes.Contains(CardType.UnAvailable) && GameData.Instance.CheckPropCanUse(1, item.CardNumber))
            {
                m_TiersData[item.Level - 1].AllCard[TierData.ALL_ITEMS].Add(item);
                m_TiersData[item.Level - 1].AllCard[TierData.ITEMS].Add(item);
            }
        }
        foreach (var weapon in GameData.Instance.AllWeapon)
        {
            if (!weapon.IsLock && GameData.Instance.CheckPropCanUse(0, weapon.CardNumber))
            {
                m_TiersData[weapon.Level - 1].AllCard[TierData.ALL_ITEMS].Add(weapon);
                m_TiersData[weapon.Level - 1].AllCard[TierData.WEAPONS].Add(weapon);
            }
        }
        foreach (var upgrade in GameData.Instance.AllLevelUpCards)
        {
            if (!upgrade.IsLock && GameData.Instance.CheckPropCanUse(2, upgrade.CardNumber))
            {
                m_TiersData[upgrade.Level - 1].AllCard[TierData.ALL_ITEMS].Add(upgrade);
                m_TiersData[upgrade.Level - 1].AllCard[TierData.UPGRADES].Add(upgrade);
            }
        }
    }

    public PropCard ProcessItemBox(int wave, PickupType pickupType)
    {
        return (PropCard)GetRandItemFromWave(wave, TierData.ITEMS, RunData.ins.LockedShopItems.Values.ToList(), null, pickupType);
    }
    public List<ShopItem> GetSelectShopItems(int wave)
    {
        List<ShopItem> new_items = new List<ShopItem>();
        //第一条优先添加武器，其次属性
        //武器没有满，添加复制道具
        if (RunData.ins.StartingWeapon != null && RunData.ins.HasWeaponSlotAvailable(RunData.ins.StartingWeapon.AttackType))
        {
            if (Random.value < 0.7f)
                new_items.Add(new ShopItem(GameData.Instance.AllProp.ToList().Find(x => x.CardNumber == 100171), wave));
            else
                new_items.Add(new ShopItem(GetUpgradeData(wave), wave));
        }
        else
        {
            if (Random.value < 0.8f)
                new_items.Add(new ShopItem(GetUpgradeData(wave), wave));
            else
                new_items.Add(new ShopItem(GetRandItemFromWave(wave, TierData.ITEMS, new_items), wave));
        }

        //第二条属性
        new_items.Add(new ShopItem(GetUpgradeData(wave), wave));

        //第三条道具 包含工程学
        if (RunData.ins.StartingWeapon != null && RunData.ins.StartingWeapon.EffectContainType(typeof(TurretEffect)))
        {
            float temp = Random.value;
            if (temp < 0.4f)
                new_items.Add(new ShopItem(GetUpgradeData(wave), wave));
            else if (temp < 0.8f)
                new_items.Add(new ShopItem(GetRandItemFromWave(wave, TierData.ITEMS, new_items, null, PickupType.None, 2), wave));
            else
                new_items.Add(new ShopItem(GetRandItemFromWave(wave, TierData.ITEMS, new_items, null, PickupType.None, 1), wave));
        }
        else
        {
            if(Random.value < 0.8f)
                new_items.Add(new ShopItem(GetUpgradeData(wave), wave));
            else
                new_items.Add(new ShopItem(GetRandItemFromWave(wave, TierData.ITEMS, new_items, null, PickupType.None, 1), wave));
        }

        return new_items;
    }
    public List<ShopItem> GetShopItems (int wave,int number = 4,List<ShopItem> shop_items = null,List<ShopItem> locked_items = null)
    {
        List<ShopItem> new_items = new List<ShopItem>();
        int nb_weapons_guaranteed = 0;
        int nb_weapons_added = 0;
        int nb_locked_weapon = 0;
        int nb_locked_item = 0;

        if(locked_items != null)
        {
            foreach (var locked_item in locked_items)
            {
                if (locked_item.Card is PropCard)
                {
                    nb_locked_item += 1;
                }
                else if (locked_item.Card is WeaponCard)
                {
                    nb_locked_weapon += 1;
                }
            }
        }
        int currentWave = RunData.GameWave + 1;

        if (currentWave < MAX_WAVE_TWO_WEAPONS_GUARANTEED)
        {
            nb_weapons_guaranteed = 2;
        }
        else if(currentWave < MAX_WAVE_ONE_WEAPON_GUARANTEED)
        {
            nb_weapons_guaranteed = 1;
        }
        if(RunData.ins.Effects.GetEffect<int>("minimum_weapons_in_shop") > nb_weapons_guaranteed)
        {
            nb_weapons_guaranteed = RunData.ins.Effects.GetEffect<int>("minimum_weapons_in_shop");
        }

        for (int i = 0; i < number; i++)
        {
            TierData tierData;
            if(currentWave <= MAX_WAVE_TWO_WEAPONS_GUARANTEED)
            {
                if(nb_weapons_added + nb_locked_weapon < nb_weapons_guaranteed)
                {
                    tierData = TierData.WEAPONS;
                }
                else
                {
                    tierData = TierData.ITEMS;
                }
            }
            else
            {
                if(Random.value < CHANCE_WEAPON || (nb_weapons_added + nb_locked_weapon) < nb_weapons_guaranteed)
                {
                    tierData = TierData.WEAPONS;
                }
                else
                {
                    tierData = TierData.ITEMS;
                }
            }
            if(tierData == TierData.WEAPONS)
            {
                nb_weapons_added += 1;
            }
            if(RunData.ins.Effects.GetEffect<int>("weapon_slot") <= 0)
            {
                tierData = TierData.ITEMS;
            }
            new_items.Add(new ShopItem(GetRandItemFromWave(wave, tierData, new_items, shop_items), wave));
        }
        return new_items;
    }

    List<CardBase> GetPool(int item_tier, TierData type)
    {
        return new List<CardBase>(m_TiersData[item_tier].AllCard[type]);
    }

    public CardBase GetRandItemFromWave(int wave, TierData type, List<ShopItem> shop_items, List<ShopItem> prev_shop_items = null, PickupType pickupType = PickupType.None, int Tag = 0)
    {
        List<ShopItem> excluded_items = new List<ShopItem>();
        excluded_items.AddRange(shop_items);
        if (prev_shop_items != null)
        {
            excluded_items.AddRange(prev_shop_items);
        }
        float rand_wanted = Random.value;
        int item_tier = GetTierFormWave(wave);
        if (pickupType == PickupType.LegendaryBox)
        {
            item_tier = (int)Tier.LEGENDARY;
        }
        if (type == TierData.WEAPONS)
        {
            item_tier = Mathf.Clamp(item_tier, RunData.ins.Effects.GetEffect<int>("min_weapon_tier") - 1, RunData.ins.Effects.GetEffect<int>("max_weapon_tier") - 1);
        }
        List<CardBase> pool = GetPool(item_tier, type);
        List<CardBase> backup_pool = GetPool(item_tier, type);

        List<CardBase> items_to_remove = new List<CardBase>();
        foreach (var shop_item in excluded_items)
        {
            if (pool.Contains(shop_item.Card))
            {
                pool.Remove(shop_item.Card);
            }
        }

        if(type == TierData.WEAPONS)
        {
            float bonus_chance_same_weapon_class = Mathf.Max(0, (MAX_WAVE_ONE_WEAPON_GUARANTEED + 1 - RunData.GameWave) * (BONUS_CHANCE_SAME_WEAPON_CLASS / MAX_WAVE_ONE_WEAPON_GUARANTEED));
            float chance_same_weapon_class = CHANCE_SAME_WEAPON_CLASS + bonus_chance_same_weapon_class;
            if(RunData.ins.Effects.GetEffect<int>("no_melee_weapons") > 0)
            {
                foreach (var item in pool)
                {
                    if((item as WeaponCard).AttackType != AttackType.SHOOT)
                    {
                        backup_pool.Remove(item);
                        items_to_remove.Add(item);
                    }
                }
            }
            if(RunData.ins.Effects.GetEffect<int>("no_ranged_weapons") > 0)
            {
                foreach (var item in pool)
                {
                    if ((item as WeaponCard).AttackType == AttackType.SHOOT)
                    {
                        backup_pool.Remove(item);
                        items_to_remove.Add(item);
                    }
                }
            }
            if(RunData.ins.Weapons.Count > 0)
            {
                if(rand_wanted < CHANCE_SAME_WEAPON)
                {
                    List<int> player_weapon_ids = new List<int>();
                    int nb_potential_same_weapons = 0;
                    foreach (var weapon in RunData.ins.Weapons)
                    {
                        foreach (var item in pool)
                        {
                            if(item.CardNumber == weapon.CardNumber)
                            {
                                nb_potential_same_weapons ++;
                            }
                        }
                        player_weapon_ids.Add(weapon.CardNumber);
                    }
                    if(nb_potential_same_weapons > 0)
                    {
                        foreach (var item in pool)
                        {
                            if (!player_weapon_ids.Contains(item.CardNumber))
                            {
                                items_to_remove.Add(item);
                            }
                        }
                    }
                } 
                else if(rand_wanted < chance_same_weapon_class)
                {
                    List<CardType> player_weapon_classes = new List<CardType>();
                    int nb_potential_same_classes = 0;
                    foreach (var weapon in RunData.ins.Weapons)
                    {
                        foreach (var weapon_class in weapon.CardTypes)
                        {
                            if (!player_weapon_classes.Contains(weapon_class))
                            {
                                player_weapon_classes.Add(weapon_class);
                            }
                        }
                    }
                    List<CardBase> weapons_to_potentially_remove = new List<CardBase>();
                    foreach (var item in pool)
                    {
                        bool item_has_atleast_one_class = false;
                        foreach (var weapon_class in player_weapon_classes)
                        {
                            if (item.CardTypes.Contains(weapon_class))
                            {
                                nb_potential_same_classes += 1;
                                item_has_atleast_one_class = true;
                                break;
                            }
                        }
                        if (!item_has_atleast_one_class)
                        {
                            weapons_to_potentially_remove.Add(item);
                        }
                    }

                    if(nb_potential_same_classes > 0)
                    {
                        foreach (var item in weapons_to_potentially_remove)
                        {
                            items_to_remove.Add(item);
                        }
                    }
                }
            }
        }
        else if(type == TierData.ITEMS && Random.value < CHANCE_WANTED_ITEM_TAG && RunData.ins.CurrentCharacter)
        {
            foreach (var item in pool)
            {
                bool has_wanted_tag = false;
                foreach (var tag in (item as PropCard).Tags)
                {
                    if (RunData.ins.CurrentCharacter.WantedTags.Contains(tag))
                    {
                        has_wanted_tag = true;
                    }
                }
                if (!has_wanted_tag)
                {
                    items_to_remove.Add(item);
                }
            }
        }
        if(Tag > 0)
        {
            foreach (var item in pool)
            {
                //包含工程系的移除
                if(!items_to_remove.Contains(item) && Tag == 1 && item.EffectContainType(typeof(TurretEffect)))
                {
                    items_to_remove.Add(item);
                }
                //不包含工程系的移除
                if (!items_to_remove.Contains(item) && Tag == 2 && !item.EffectContainType(typeof(TurretEffect)))
                {
                    items_to_remove.Add(item);
                }
            }
        }
        Dictionary<int, LimiteItem> limited_items = new Dictionary<int, LimiteItem>();
        foreach (var item in RunData.ins.Items)
        {
            if(item.CardTypes.Contains(CardType.Unique))
            {
                backup_pool.Remove(item);
                items_to_remove.Add(item);
            }
            else if (item.CardTypes.Contains(CardType.Astrict))
            {
                PropCard prop = (PropCard)item;
                if (limited_items.ContainsKey(item.CardNumber))
                {
                    limited_items[item.CardNumber].Number++;
                }
                else
                {
                    limited_items.Add(item.CardNumber, new LimiteItem(prop, 1));
                }
            }
        }

        foreach (var item in limited_items.Values)
        {
            if(item.Number >= item.Card.NumberLimit)
            {
                backup_pool.Remove(item.Card);
                items_to_remove.Add(item.Card);
            }
        }
        foreach (var item in items_to_remove)
        {
            pool.Remove(item);
        }

        CardBase elt = null;
        if(pool.Count <= 0)
        {
            if(backup_pool.Count > 0)
            {
                elt = backup_pool.GetRandomElementList();
            }
            else
            {
                elt = m_TiersData[item_tier].AllCard[type].GetRandomElementList();
            }
        }
        else
        {
            elt = pool.GetRandomElementList();
        }

        return elt;
    }

    public int GetTierFormWave(int wave)
    {
        float rand = Random.value;

        int tier = 0;
        float luck = RunData.ins.GetAllStat("stat_luck") * 0.01f;

        for (int i = m_TiersData.Length - 1; i >= 0 ; i--)
        {
            TiersData item = m_TiersData[i];
            float wave_base_chance = Mathf.Max(0f, ((wave - 1) - item.MinWave) * item.WaveBonusChance);
            float wave_chance = 0f;
            if (luck >= 0f)
            {
                wave_chance = wave_base_chance * (1f + luck);
            }
            else
            {
                wave_chance = wave_base_chance / (1f + Mathf.Abs(luck));
            }
            float chance = item.BaseChance + wave_chance;
            float max_chance = item.MaxChance;
            if (rand <= Mathf.Min(chance, max_chance))
            {
                tier = i;
                break;
            }
        }
        return tier;
    }

    public LevelUpCard[] GetUpgrades(int level, LevelUpCard[] old_upgrades)
    {
        LevelUpCard[] upgrades_to_show = new LevelUpCard[4];

        for (int i = 0; i < 4; i++)
        {
            LevelUpCard upgrade = GetUpgradeData(level);
            int tries = 0;
            while((upgrades_to_show.Contains(upgrade) || old_upgrades.Contains(upgrade)) && tries < 50)
            {
                upgrade = GetUpgradeData(level);
                tries++;
            }
            upgrades_to_show[i] = upgrade;
        }
        return upgrades_to_show;
    }
    public LevelUpCard GetUpgradeData(int level)
    {
        int tier = GetTierFormWave(level);
        if(level == 5)
        {
            tier = (int)Tier.UNCOMMON;
        }
        else if(level == 10 || level == 15)
        {
            tier = (int)Tier.RARE;
        }
        else if(level % 5 == 0)
        {
            tier = (int)Tier.LEGENDARY;
        }
        LevelUpCard levelUpCard = (LevelUpCard)m_TiersData[tier].AllCard[TierData.UPGRADES].GetRandomElementList();
        //闪避或者暴击满之后不再出现对应道具
        while((levelUpCard.EffectContainStat("stat_dodge") && RunData.ins.GetAllStat("stat_dodge") >= RunData.ins.Effects.GetEffect<int>("dodge_cap"))
            || levelUpCard.EffectContainStat("stat_crit_chance") && RunData.ins.GetAllStat("stat_crit_chance") >= 100)
        {
            levelUpCard = (LevelUpCard)m_TiersData[tier].AllCard[TierData.UPGRADES].GetRandomElementList();
        }
        return levelUpCard;
    }
	public Sprite GetLevelSprite(bool isIcon,int level)
    {
        Sprite[] sprites;
        if (isIcon)
        {
            sprites = LevelHeadBackground;
        }
        else
        {
            sprites = LevelMaskBackground;
        }
        if (level < 0)
        {
            return sprites[0];
        }
        else if(level >= sprites.Length)
        {
            return sprites[sprites.Length - 1];
        }
        else
        {
            return sprites[level];
        }
    }
    public int GetValue(int wave, int base_value, bool affected_by_items_price_stat = true, bool isWeapon = false)
    {
        float value_after_weapon_price = base_value;
        if(isWeapon && affected_by_items_price_stat)
        {
            value_after_weapon_price = base_value * (1f + RunData.ins.Effects.GetEffect<int>("weapons_price") * 0.01f);
        }
        float items_price_factor = 1f;
        float diff_factor = 0;
        float endless_factor = 0;
        if (affected_by_items_price_stat)
        {
            items_price_factor = (1f + RunData.ins.Effects.GetEffect<int>("items_price") * 0.01f);
            diff_factor = (RunData.ins.Effects.GetEffect<int>("inflation") * 0.01f);
            endless_factor = RunData.ins.GetEndLessFactor(wave) / 5f;
        }
        return (int)Mathf.Max(1, ((value_after_weapon_price + wave + (value_after_weapon_price * wave * (0.1f + diff_factor + endless_factor))) * items_price_factor));
    }

    public int GetRerollPrice(int wave,int last_reroll_value)
    {
        return (int)Mathf.Max(1f, last_reroll_value + Mathf.Max(1f, 0.5f * wave * (1 + RunData.ins.GetEndLessFactor())));
    }
    public int GetRecyclingValue(int wave,int from_value, bool isWeapon = false, bool affected_by_items_price_stat = true)
    {
        var actually_affected = affected_by_items_price_stat && RunData.GameWave < RunData.NumberofWaves;
        return (int)Mathf.Max(1f, (GetValue(wave, from_value, actually_affected, isWeapon) * Mathf.Clamp(0.25f + (RunData.ins.Effects.GetEffect<int>("recycling_gains") * 0.01f), 0.01f, 0.1f)));
    }
    public SuitCardEffect GetSet(CardType type)
    {
        foreach (var item in Sets)
        {
            if(item.WeaponClass == type)
            {
                return item;
            }
        }
        return null;
    }

    private class LimiteItem
    {
        public PropCard Card;
        public int Number;

        public LimiteItem(PropCard card, int number)
        {
            Card = card;
            Number = number;
        }
    }
}

public class TiersData
{
    public Dictionary<TierData, List<CardBase>> AllCard;
    public float MinWave;
    public float BaseChance;
    public float WaveBonusChance;
    public float MaxChance;

    public TiersData(float minWave, float baseChance, float waveBonusChance, float maxChance)
    {
        AllCard = new Dictionary<TierData, List<CardBase>>(new TierDataComparer())
        {
            [TierData.ALL_ITEMS] = new List<CardBase>(),
            [TierData.ITEMS] = new List<CardBase>(),
            [TierData.WEAPONS] = new List<CardBase>(),
            [TierData.UPGRADES] = new List<CardBase>(),
        };
        MinWave = minWave;
        BaseChance = baseChance;
        WaveBonusChance = waveBonusChance;
        MaxChance = maxChance;
    }

    private class TierDataComparer : IEqualityComparer<TierData>
    {
        public bool Equals(TierData x, TierData y)
        {
            return x == y;
        }

        public int GetHashCode(TierData obj)
        {
            return (int)obj;
        }
    }
}

public enum TierData
{
    ALL_ITEMS,
    ITEMS,
    WEAPONS,
    CONSUMABLES,
    UPGRADES,
    MIN_WAVE,
    BASE_CHANCE,
    WAVE_BONUS_CHANCE,
    MAX_CHANCE
}
public enum Tier { COMMON, UNCOMMON, RARE, LEGENDARY, DANGER_4, DANGER_5, DANGER_0 }


