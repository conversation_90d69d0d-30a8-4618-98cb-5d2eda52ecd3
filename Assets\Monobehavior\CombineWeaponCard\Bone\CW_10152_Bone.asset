%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 69ac6b95ef75bd74e856ed39990d2715, type: 3}
  m_Name: CW_10152_Bone
  m_EditorClassIdentifier: 
  ID: 0
  CardNumber: 10152
  CardTypes: 
  ItemName: Txt_CombineWeapon_Name_Bone
  ItemType: 
  IconPath: Image\UI\CombineWeapon\UI_wq_gt_02
  PropertyDescribe: 
  Effects: []
  UpgradeIds: 
  UpgradeDatas: []
  Remarks: 
  rarity: 1
  starLevel: 2
  Cost: 0
  weaponCard: {fileID: 11400000, guid: 6b867e033f07f0a47b444bfe1605d217, type: 2}
  price: 3
  ChildWeaponIds: 10151,10151
  ChildCombineWeapons: []
  ParentCombineWeapons: []
  weaponType: 16
  damageType: 0
  range: 800
  baseAttack: 100
  physicalAttackCoefficient: 1.2
  magicPowerCoefficient: 0
  hpAttackCoefficient: 0
  speedAttackCoefficient: 0
  criticalChance: 15
  criticalDamage: 60
  attackInterval: 1.67
  ProjectilePrefabPath: Prefabs\Projectiles\BulletProjectileBolt_bone
  ProjectileAudioClip: Sounds\Ranged\shotgun_01,Sounds\Ranged\shotgun_02,Sounds\Ranged\shotgun_03,Sounds\Ranged\shotgun_04
  weaponName: Bone
