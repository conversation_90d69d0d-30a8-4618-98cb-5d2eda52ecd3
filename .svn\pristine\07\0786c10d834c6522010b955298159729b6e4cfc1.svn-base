﻿using UnityEngine;

public class Fly : MonsterController
{
    public float ProjChance = 0.25f;

    public override void OnHurtBoxAreaEntered(HitBox hit)
    {
        base.OnHurtBoxAreaEntered(hit);
        if(hit.From != null)
        {
            if(hit.From is RangeWeapon && Random.value < ProjChance)
            {
                m_CurrentAttackBehavior.Shoot();
            }
        }
    }
    public override void OnHurt()
    {
        base.OnHurt();
        if(Random.value < ProjChance)
        {
            m_CurrentAttackBehavior.Shoot();
        }
    }
}
