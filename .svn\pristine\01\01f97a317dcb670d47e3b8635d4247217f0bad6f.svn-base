﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[CreateAssetMenu(fileName = "ExplodingEffect",menuName = "Effect/ExplodingEffect")]
public class ExplodingEffect : NullEffect
{
    [Range(0.01f,1f)]
    public float Chance = 1f;
    public float Scale = 1f;
    public int BaseSmokeAmount = 40;
    public float SoundDBMod = 0.1f;

    public override string GetID()
    {
        return "weapon_exploding";
    }
    public override string[] GetArgs(RunData RunData, CardBase card, int Level, int EquipId)
    {
        return new string[] { Mathf.FloorToInt((Chance * 100)).ToString()};
    }
}
