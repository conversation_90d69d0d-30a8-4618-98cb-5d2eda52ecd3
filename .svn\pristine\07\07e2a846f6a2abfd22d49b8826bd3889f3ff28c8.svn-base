%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e1ce0997cc050664fb2b4cbd603d4f9e, type: 3}
  m_Name: Tentacle_Stats
  m_EditorClassIdentifier: 
  PrefabPath: Prefabs\Enemies\Monster_Tentacle
  Health: 100
  HealthIncreaseEachWave: 20
  Speed: 175
  SpeedRandomization: 0
  Damage: 1
  DamageIncreaseEachWave: 1
  CoinsDropped: 1
  BaseDropChance: 0.01
  ItemDropChance: 0.01
  AttackCD: 30
  knockback_resistance: 0.7
  can_drop_consumables: 1
  always_drop_consumables: 0
  Armor: 0
  ArmorIncreaseEachWave: 0
  UnitInformation: Txt_MonsterInfo_Tentacle
  Remarks: 
