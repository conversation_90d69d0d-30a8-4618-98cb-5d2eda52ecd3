%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 35421ad95d36d3c4abdfe0b2a4b84db6, type: 3}
  m_Name: "LaserGun_Stats \u2161"
  m_EditorClassIdentifier: 
  coolDown: 100
  damage: 45
  accuracy: 1
  critChance: 0.03
  critDamage: 2
  minRange: 0
  maxRange: 500
  knockback: 0
  effectScale: 1
  lifeSteal: 0
  Remarks: 
  ScalingStats:
  - StatName: stat_ranged_damage
    StatScale: 4.5
  ShootingSoundPaths:
  - Sounds\Ranged\sniper1_04
  - Sounds\Ranged\sniper1_04v2
  IsHealing: 0
  CustomOnCooldownSpritePath: 
  Recoil: 40
  RecoilDuration: 0.2
  AdditionalCooldownEveryXShots: -1
  AdditionalCooldownMultiplier: -1
  NbProjectiles: 1
  ProjectileSpread: 0
  Piercing: 1
  PiercingDmgReduction: 0.25
  Bounce: 0
  BounceDmgReduction: 0.5
  ProjectileSpeed: 3000
  InCreaseProjectileSpeedWithRange: 0
  ProjectilePrefabPath: Prefabs\Projectiles\BulletProjectileLaser
