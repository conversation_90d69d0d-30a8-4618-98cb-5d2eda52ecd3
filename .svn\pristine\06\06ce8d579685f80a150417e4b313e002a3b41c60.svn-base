﻿using UnityEngine;
using System;
#if Platform_DY
using StarkSDKSpace;
#endif

public class DYReward : CADManager<DYReward>
{
    private string APPID = "";
    private string ADUINTID = "";

    private bool isLoaded;

    public override void SetAPPKEY(string appKey)
    {
        APPID = appKey;
    }

    public override void SetPOSID(string posID)
    {
        ADUINTID = posID;
    }
    public override EADSDKType SDKType
    {
        get
        {
            return EADSDKType.DYReward;
        }
    }

    public override EADStyle ADStyle
    {
        get { return EADStyle.RewardVideo; }
    }

    public static DYReward instance;
    protected override void Awake()
    {
        instance = this;
        base.Awake();
        this.gameObject.name = "DYReward";
    }

#if Platform_DY
    StarkAdManager adMgr = null;
#endif
    public override void InitSDK()
    {
        m_IsDebug = true;
#if Platform_DY
        adMgr = StarkSDK.API.GetStarkAdManager();
#endif
    }

#if Platform_DY
    class RewardCallback : StarkAdManager.VideoAdCallback
    {
        public void OnError(int errCode, string errorMessage)
        {
            instance.OnADLoadFail(errorMessage);
        }

        public void OnVideoClose(int watchedTime, int effectiveTime, int duration)
        {
            Debug.Log("OnVideoClose watchedTime:"+ watchedTime+ "  effectiveTime:"+ effectiveTime);
            if (watchedTime >= effectiveTime)
                instance.OnADComplete();
            instance.OnADClose();
        }

        public void OnVideoLoaded()
        {
            instance.OnADLoadOk();
        }

        public void OnVideoShow(long timestamp)
        {
            //instance.OnADShow();
        }
    }
#endif

    public override void ADLoad(string userId, bool useCount = false)
    {
        base.ADLoad(userId, useCount);
        if (m_WhenLoadFailTimes <= 0)
        {
            return;
        }

        isLoaded = true;
    }

    public override bool ADShow()
    {
#if Platform_DY
        if (adMgr != null)
        {
            adMgr.ShowVideoAdWithId(ADUINTID,
            (IsComplete) => {
                //close
                if (IsComplete)
                    OnADComplete();
                OnADClose();
            },
            (id, str) => {
                //error
                OnADLoadFail(str);
            }
            //other
            //new RewardCallback()
            );

            OnADShow();
            isLoaded = false;
            return true;
        }
        else
#endif
        {
            return false;
        }
    }

    public override bool HasCached()
    {
        return HasCachedAll();
    }

    public override bool HasCachedAll()
    {
        return isLoaded;
    }


#region AD Message from IOS/ANDROID

    /// <summary>
    /// 开始播放
    /// </summary>
    public void OnADShow()
    {
        if (m_IsDebug)
        {
            Debug.Log("from android/ios, OnADShow");
        }
        if (ADMgr.Instance != null)
        {
            ADMgr.Instance.TriggerADShow(SDKType, "ADVIDEO_SHOW");
        }
    }

    /// <summary>
    /// 广告关闭
    /// </summary>
    public void OnADClose()
    {
        if (m_IsDebug)
        {
            Debug.Log("from android/ios, OnADClose  m_IsCompleted:"+ m_IsCompleted.ToString());
        }

        if (ADMgr.Instance != null)
        {
            ADMgr.Instance.TriggerADClose(SDKType, "ADVIDEO_CLOSE");
        }

        if (m_IsCompleted)
        {
            ADMgrListener.Instance.RequestReward(SDKType);
            m_IsCompleted = false;
        }
    }

    /// <summary>
    /// 广告播放完成
    /// </summary>
    public void OnADComplete()
    {
        if (m_IsDebug)
        {
            Debug.Log("from android/ios, OnADComplete");
        }

        if (ADMgr.Instance != null)
        {
            ADMgr.Instance.TriggerADComplete(SDKType, "ADVideoComplete");
        }

        m_IsCompleted = true;
    }


    /// <summary>
    /// 广告加载失败
    /// </summary>
    public void OnADLoadFail(string code_message)
    {
        if (m_IsDebug)
        {
            Debug.Log(code_message);
        }

        if (ADMgr.Instance != null)
        {
            if (m_WhenLoadFailIsReload)
            {
                ADMgr.Instance.TriggerADLoadFail(SDKType, "ADVideoLoadFail");
            }
        }
    }

    /// <summary>
    /// 广告加载成功
    /// </summary>
    public void OnADLoadOk()
    {
        if (m_IsDebug)
        {
            Debug.Log("from android/ios, OnADLoadOk");
        }
    }

    /// <summary>
    /// 广告被点击
    /// </summary>
    public void OnADClicked()
    {
        if (m_IsDebug)
        {
            Debug.Log("from android/ios, OnADClicked");
        }
    }

#endregion
}