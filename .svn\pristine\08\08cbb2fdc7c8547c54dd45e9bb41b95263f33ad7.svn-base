%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 35421ad95d36d3c4abdfe0b2a4b84db6, type: 3}
  m_Name: 'GhostScepter_Stats '
  m_EditorClassIdentifier: 
  coolDown: 50
  damage: 10
  accuracy: 1
  critChance: 0.03
  critDamage: 2
  minRange: 0
  maxRange: 300
  knockback: 2
  effectScale: 1
  lifeSteal: 0
  Remarks: 
  ScalingStats:
  - StatName: stat_ranged_damage
    StatScale: 1
  ShootingSoundPaths:
  - Sounds\Ranged\pea_02
  IsHealing: 0
  CustomOnCooldownSpritePath: 
  Recoil: 25
  RecoilDuration: 0.1
  AdditionalCooldownEveryXShots: -1
  AdditionalCooldownMultiplier: -1
  NbProjectiles: 1
  ProjectileSpread: 0
  Piercing: 0
  PiercingDmgReduction: 0.5
  Bounce: 0
  BounceDmgReduction: 0.5
  ProjectileSpeed: 3000
  InCreaseProjectileSpeedWithRange: 0
  ProjectilePrefabPath: Prefabs\Projectiles\BulletProjectileGhost
