%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 35421ad95d36d3c4abdfe0b2a4b84db6, type: 3}
  m_Name: "SniperGun_Stats \u2163"
  m_EditorClassIdentifier: 
  coolDown: 120
  damage: 80
  accuracy: 1
  critChance: 0.2
  critDamage: 2
  minRange: 0
  maxRange: 1000
  knockback: 30
  effectScale: 1
  lifeSteal: 0
  Remarks: 
  ScalingStats:
  - StatName: stat_ranged_damage
    StatScale: 1
  - StatName: stat_range
    StatScale: 0.3
  ShootingSoundPaths:
  - Sounds\Ranged\sniper1_04
  - Sounds\Ranged\sniper1_04v2
  IsHealing: 0
  CustomOnCooldownSpritePath: 
  Recoil: 40
  RecoilDuration: 0.2
  AdditionalCooldownEveryXShots: -1
  AdditionalCooldownMultiplier: -1
  NbProjectiles: 1
  ProjectileSpread: 0
  Piercing: 0
  PiercingDmgReduction: 0.5
  Bounce: 0
  BounceDmgReduction: 0.5
  ProjectileSpeed: 3000
  InCreaseProjectileSpeedWithRange: 0
  ProjectilePrefabPath: Prefabs\Projectiles\BulletProjectileLaser
