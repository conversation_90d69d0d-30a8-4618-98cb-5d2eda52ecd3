﻿using UnityEngine;
using System.Collections;
using System.Runtime.InteropServices;
using System;
#if Google_Channel
using GoogleMobileAds.Api;
#endif


public class GADReward : CADManager<GADReward>
{
    private string AppKey = "";
    private bool isLoaded = false;
    public override void SetAPPKEY(string appKey)
    {
        AppKey = appKey;
    }
    public override void SetPOSID(string posID)
    {
        PosID = posID;
    }
    public override EADSDKType SDKType
    {
        get
        {
            return EADSDKType.GADReward;
        }
    }

    public override EADStyle ADStyle
    {
        get
        {
            return EADStyle.RewardVideo;
        }
    }

    protected override void Awake()
    {
        base.Awake();
        this.gameObject.name = "GADReward";
    }

    public override void InitSDK()
    {
        m_IsDebug = Debug.isDebugBuild;
#if UNITY_IOS && !UNITY_EDITOR && Google_Channel
        initGoogleAdMobSDK();
#endif
    }

#if Google_Channel
    RewardedAd currentAd = null;
#endif
    public override void ADLoad(string userId, bool useCount = false)
    {
        base.ADLoad(userId, useCount);
        if (m_WhenLoadFailTimes <= 0)
        {
            return;
        }
#if UNITY_IOS && !UNITY_EDITOR && Google_Channel
        isLoaded = false;
        loadGoogleAdMobReward(ADUINTID);
#endif

#if UNITY_ANDROID && Google_Channel
        isLoaded = false;
        if (currentAd != null)
        {
            currentAd.Destroy();
            currentAd = null;
        }

        //老版本
        //if (this.currentAd == null)
        //{
        //    this.currentAd = new RewardedAd(PosID);
        //    //当广告加载完毕
        //    currentAd.OnAdLoaded += (object sender, EventArgs args) =>
        //    {
        //        OnADLoadOk();
        //    };
        //    //当广告加载失败
        //    currentAd.OnAdFailedToLoad += (object sender, AdFailedToLoadEventArgs args) =>
        //    {
        //        OnADLoadFail(args.LoadAdError.ToString());
        //    };
        //    //当广告被点击打开时
        //    currentAd.OnAdOpening += (object sender, EventArgs args) =>
        //    {
        //        OnADShow();
        //    };
        //    //当广告显示失败时调用
        //    currentAd.OnAdFailedToShow += (object sender, AdErrorEventArgs args) =>
        //    {
        //        if (ADMgr.Instance != null)
        //        {
        //            if (m_WhenLoadFailIsReload)
        //            {
        //                ADMgr.Instance.TriggerADLoadFail(SDKType, "ADVideoLoadFail");
        //            }
        //        }

        //        if (m_IsDebug)
        //        {
        //            Debug.Log(args.ToString());
        //        }
        //    };
        //    //在用户因观看视频而应获得奖励时被调用
        //    currentAd.OnUserEarnedReward += (object sender, Reward r) =>
        //    {
        //        if (currentAd != null)
        //        {
        //            currentAd.Destroy();
        //            currentAd = null;
        //        }
        //        OnADComplete();
        //    };
        //    //在用户点按“关闭”图标或使用“返回”按钮关闭激励视频广告时被调用。
        //    currentAd.OnAdClosed += (object sender, EventArgs args) =>
        //    {
        //        OnADClose();
        //        ADLoad("");
        //    };

        //    this.currentAd.LoadAd(new AdRequest.Builder().Build());
        //}

        // create new rewarded ad instance, send the request to load the ad.
        RewardedAd.Load(PosID, new AdRequest(),
            (RewardedAd ad, LoadAdError error) =>
            {
                if (error != null || ad == null)
                {
                    Debug.Log("Rewarded ad failed to load an ad with error : " + error);
                    OnADLoadFail(error.GetMessage());
                }
                else
                {
                    Debug.Log("Rewarded ad loaded with response : " + ad.GetResponseInfo());
                    OnADLoadOk();

                    ad.OnAdPaid += (v) => { };
                    ad.OnAdFullScreenContentClosed += () =>
                    {
                        OnADClose();
                        ADLoad("");
                    };
                    ad.OnAdFullScreenContentFailed += (e) =>
                    {
                        Debug.Log("Rewarded ad failed to open full screen content with error : " + error);
                        ADLoad("");
                    };
                    ad.OnAdImpressionRecorded += () =>
                    {
                        //OnADLoadOk();
                    };
                    ad.OnAdFullScreenContentOpened += OnADShow;
                    ad.OnAdClicked += OnADClicked;
                    currentAd = ad;
                }
            });
#endif
    }

    public override bool ADShow()
    {
        if (isLoaded)
        {
#if UNITY_IOS && !UNITY_EDITOR && Google_Channel
            showGoogleAdReward();
            isLoaded = false;
#endif
#if UNITY_ANDROID && Google_Channel
            isLoaded = false;
            if (currentAd != null && currentAd.CanShowAd())
            {
                currentAd.Show((Reward reward) =>
                {
                    Debug.Log(string.Format("Rewarded ad rewarded the user. Type: {0}, amount: {1}.", reward.Type, reward.Amount));
                    OnADComplete();
                });
            }
            //if (currentAd != null && currentAd.IsLoaded())
            //{
            //    currentAd.Show();
            //}
#endif
            return true;
        }
        return false;
    }

    public override bool HasCached()
    {
        return HasCachedAll();
    }

    public override bool HasCachedAll()
    {
        //if (this.currentAd != null)
        //{
        //    return this.currentAd.IsLoaded();
        //}
        return isLoaded;
    }

#region AD Message from IOS/ANDROID

    /// <summary>
    /// 广告加载失败
    /// </summary>
    public void OnADLoadFail(string code_message)
    {
        if (ADMgr.Instance != null)
        {
            if (m_WhenLoadFailIsReload)
            {
                ADMgr.Instance.TriggerADLoadFail(SDKType, "ADVideoLoadFail");
            }
        }

        if (m_IsDebug)
        {
            Debug.Log(code_message);
        }
    }

    /// <summary>
    /// 广告加载成功
    /// </summary>
    public void OnADLoadOk()
    {
        isLoaded = true;
        ADMgr.Instance.TriggerADLoadOk(SDKType, "ADVIDEO_LOAD_SUCCESS");
        if (m_IsDebug)
        {
            Debug.Log("from android/ios, OnADLoadOk");
        }
    }

    /// <summary>
    /// 开始播放
    /// </summary>
    public void OnADShow()
    {
        if (m_IsDebug)
        {
            Debug.Log("from android/ios, OnADShow");
        }
        if (ADMgr.Instance != null)
        {
            ADMgr.Instance.TriggerADShow(SDKType, "ADVIDEO_SHOW");
        }
    }

    /// <summary>
    /// 广告关闭
    /// </summary>
    public void OnADClose()
    {
        if (m_IsDebug)
        {
            Debug.Log("from android/ios, OnADClose");
        }

        if (ADMgr.Instance != null)
        {
            ADMgr.Instance.TriggerADClose(SDKType, "ADVIDEO_CLOSE");
        }

        if (m_IsCompleted)
        {
            ADMgrListener.Instance.RequestReward(SDKType);
            m_IsCompleted = false;
        }
    }

    /// <summary>
    /// 广告播放完成
    /// </summary>
    public void OnADComplete()
    {
        if (m_IsDebug)
        {
            Debug.Log("from android/ios, OnADComplete");
        }

        if (ADMgr.Instance != null)
        {
            ADMgr.Instance.TriggerADComplete(SDKType, "ADVideoComplete");
        }

        m_IsCompleted = true;
    }

    /// <summary>
    /// 广告被点击
    /// </summary>
    public void OnADClicked()
    {
        if (m_IsDebug)
        {
            Debug.Log("from android/ios, OnADClicked");
        }
    }

    /// <summary>
    /// 广告跳过
    /// </summary>
    public void OnADVideoSkiped()
    {
        if (m_IsDebug)
        {
            Debug.Log("from android/ios, OnADVideoSkiped");
        }
        OnADComplete();
    }
#endregion

#if UNITY_IOS && Google_Channel

#region iOS_Internal
    [DllImport("__Internal")]
    private static extern void initGoogleAdMobSDK();

    [DllImport("__Internal")]
    private static extern void loadGoogleAdMobReward(string slotID);

    [DllImport("__Internal")]
    private static extern void showGoogleAdReward();

#endregion

#endif
}
