%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 69ac6b95ef75bd74e856ed39990d2715, type: 3}
  m_Name: CW_30006_SoulScythe
  m_EditorClassIdentifier: 
  ID: 0
  CardNumber: 30006
  CardTypes: 
  ItemName: Txt_CombineWeapon_Name_SoulScythe
  ItemType: 
  IconPath: Image\UI\CombineWeapon\UI_wq_zhl
  PropertyDescribe: 
  Effects: []
  UpgradeIds: 
  UpgradeDatas: []
  Remarks: 
  rarity: 3
  starLevel: 1
  Cost: 12
  weaponCard: {fileID: 11400000, guid: b8e87d4748e3dde409d4d9837fa246bf, type: 2}
  price: 0
  ChildWeaponIds: 20012,20101
  ChildCombineWeapons: []
  ParentCombineWeapons: []
  weaponType: 52
  damageType: 0
  range: 100
  baseAttack: 640
  physicalAttackCoefficient: 6.5
  magicPowerCoefficient: 0
  hpAttackCoefficient: 0
  speedAttackCoefficient: 0
  criticalChance: 10
  criticalDamage: 120
  attackInterval: 1.25
  ProjectilePrefabPath: Prefabs\Projectiles\BulletProjectileBolt
  ProjectileAudioClip: Sounds\Ranged\shotgun_01,Sounds\Ranged\shotgun_02,Sounds\Ranged\shotgun_03,Sounds\Ranged\shotgun_04
  weaponName: SoulScythe
