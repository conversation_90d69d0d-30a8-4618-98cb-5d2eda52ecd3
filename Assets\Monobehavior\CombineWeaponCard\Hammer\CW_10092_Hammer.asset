%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 69ac6b95ef75bd74e856ed39990d2715, type: 3}
  m_Name: CW_10092_Hammer
  m_EditorClassIdentifier: 
  ID: 0
  CardNumber: 10092
  CardTypes: 
  ItemName: Txt_CombineWeapon_Name_Hammer
  ItemType: 
  IconPath: Image\UI\CombineWeapon\UI_wq_cz_02
  PropertyDescribe: 
  Effects: []
  UpgradeIds: 
  UpgradeDatas: []
  Remarks: 
  rarity: 1
  starLevel: 2
  Cost: 0
  weaponCard: {fileID: 11400000, guid: 2758ecbdec0916947a6dae1677d961c4, type: 2}
  price: 3
  ChildWeaponIds: 10091,10091
  ChildCombineWeapons: []
  ParentCombineWeapons: []
  weaponType: 9
  damageType: 0
  range: 100
  baseAttack: 80
  physicalAttackCoefficient: 1
  magicPowerCoefficient: 0
  hpAttackCoefficient: 0
  speedAttackCoefficient: 0
  criticalChance: 15
  criticalDamage: 60
  attackInterval: 1.25
  ProjectilePrefabPath: Prefabs\Projectiles\BulletProjectile_ZKR
  ProjectileAudioClip: Sounds\Ranged\shotgun_01,Sounds\Ranged\shotgun_02,Sounds\Ranged\shotgun_03,Sounds\Ranged\shotgun_04
  weaponName: Hammer
