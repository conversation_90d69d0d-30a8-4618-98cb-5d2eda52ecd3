%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 69ac6b95ef75bd74e856ed39990d2715, type: 3}
  m_Name: CW_30015_Kingblade
  m_EditorClassIdentifier: 
  ID: 0
  CardNumber: 30015
  CardTypes: 
  ItemName: Txt_CombineWeapon_Name_Kingblade
  ItemType: 
  IconPath: Image\UI\CombineWeapon\UI_wq_jlj
  PropertyDescribe: 
  Effects: []
  UpgradeIds: 
  UpgradeDatas: []
  Remarks: 
  rarity: 3
  starLevel: 1
  Cost: 10
  weaponCard: {fileID: 11400000, guid: cb381e714e6a77d47b0998f7285dccab, type: 2}
  price: 0
  ChildWeaponIds: 20021,20131
  ChildCombineWeapons: []
  ParentCombineWeapons: []
  weaponType: 56
  damageType: 0
  range: 800
  baseAttack: 820
  physicalAttackCoefficient: 8.1
  magicPowerCoefficient: 0
  hpAttackCoefficient: 0
  speedAttackCoefficient: 0
  criticalChance: 12
  criticalDamage: 90
  attackInterval: 1.25
  ProjectilePrefabPath: Prefabs\Projectiles\BulletProjectileBolt
  ProjectileAudioClip: Sounds\Ranged\shotgun_01,Sounds\Ranged\shotgun_02,Sounds\Ranged\shotgun_03,Sounds\Ranged\shotgun_04
  weaponName: Kingblade
