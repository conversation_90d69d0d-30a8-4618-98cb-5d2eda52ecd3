%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 69ac6b95ef75bd74e856ed39990d2715, type: 3}
  m_Name: CW_20071_Wrench
  m_EditorClassIdentifier: 
  ID: 0
  CardNumber: 20071
  CardTypes: 
  ItemName: Txt_CombineWeapon_Name_Wrench
  ItemType: 
  IconPath: Image\UI\CombineWeapon\UI_wq_jxb_01
  PropertyDescribe: 
  Effects: []
  UpgradeIds: 
  UpgradeDatas: []
  Remarks: 
  rarity: 2
  starLevel: 1
  Cost: 3
  weaponCard: {fileID: 11400000, guid: 06d16293bffaba64181d756aa55a9868, type: 2}
  price: 9
  ChildWeaponIds: 10111,10092,10132
  ChildCombineWeapons: []
  ParentCombineWeapons: []
  weaponType: 31
  damageType: 1
  range: 800
  baseAttack: 320
  physicalAttackCoefficient: 0
  magicPowerCoefficient: 3.5
  hpAttackCoefficient: 0
  speedAttackCoefficient: 0
  criticalChance: 12
  criticalDamage: 70
  attackInterval: 1
  ProjectilePrefabPath: Prefabs\Projectiles\BulletProjectileSlingshot
  ProjectileAudioClip: Sounds\Ranged\shotgun_01,Sounds\Ranged\shotgun_02,Sounds\Ranged\shotgun_03,Sounds\Ranged\shotgun_04
  weaponName: Wrench
