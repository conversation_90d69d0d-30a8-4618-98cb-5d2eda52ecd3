﻿using DG.Tweening;
using I2.Loc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UnityEngine;
using UnityEngine.UI;

public class MenuSelectProp : UIMenuBase
{
    [Header("BoxItem")]
    public GameObject BoxItem;
    public ItemStatsPanel BoxItemPanel;

    [Header("LevelItem")]
    public GameObject LevelItem;
    public ItemStatsPanel[] LevelItemPanel;

    List<int> m_UpgradesProcess;
    List<ShopItem> m_ShopItems = new List<ShopItem>();
    public override void Setup()
    {
        base.Setup();
    }
    public override void Init()
    {
        base.Init();
        SetActivePanel();
    }
    private void SetActivePanel()
    {
        ZoomInWrapper();
        BoxItem.SetActive(false);
        LevelItem.SetActive(false);
        if (MainService.Instance.BoxItems.Count > 0)
        {
            BoxItem.SetActive(true);
            InitBoxItem();
        }
        else if (MainService.Instance.UpgradesToProcess.Count > 0)
        {
            LevelItem.SetActive(true);
            InitLevelItems();
        }
        else
        {
            if (ServerGameConfig.ins.mGameMode == ServerGameConfig.GameMode.Single)
            {
                SaveManager.Instance.SaveGameData(RunData.ins.GetState(new List<ShopItem>(), new LevelUpCard[] { }, 0, 0, 0, 0, 0));
            }
            UIResponse_Close();
            RunData.ResumedFromState = false;
            GameManager.Instance.CompleteWork();
        }
    }
    private void InitBoxItem()
    {
        BoxItemPanel.UpdatePanelInfor(MainService.Instance.BoxItems[0]);
    }
    public void OnClickGetBox()
    {
        RunData.ins.AddItem(MainService.Instance.BoxItems[0]);
        RemoveBoxItem();
        MissionData_SO.Instance.UpdateMissionProgress(E_MissionName.角色提升, 1, 0);
        MissionData_SO.Instance.CheckMissionGameEffect();
    }
    public void OnClickSellBox()
    {
        RemoveBoxItem();
    }
    private void RemoveBoxItem()
    {
        MainService.Instance.BoxItems.RemoveAt(0);
        if (ServerGameConfig.ins.mGameMode == ServerGameConfig.GameMode.Single)
        {
            SaveManager.Instance.SaveGameData(RunData.ins.GetState(new List<ShopItem>(), new LevelUpCard[] { }, 0, 0, 0, 0, 0));
        }
        SetActivePanel();
    }
    private void InitLevelItems()
    {
        m_ShopItems.Clear();
        if (RunData.ResumedFromState)
        {
            GameSave save = SaveManager.Instance.GameSaveData;
            if(save.ShopItems.Length > 0)
            {
                for (int i = 0; i < save.ShopItems.Length; i++)
                {
                    ValueTuple<int, int, int, bool> data = save.ShopItems[i];
                    if (data.Item1 == 0)
                    {
                        m_ShopItems.Add(new ShopItem(GameData.Instance.AllWeapon[data.Item2], data.Item3, data.Item4));
                    }
                    else if (data.Item1 == 1)
                    {
                        m_ShopItems.Add(new ShopItem(GameData.Instance.AllProp[data.Item2], data.Item3, data.Item4));
                    }
                    else
                    {
                        m_ShopItems.Add(new ShopItem(GameData.Instance.AllLevelUpCards[data.Item2], data.Item3, data.Item4));
                    }
                }
            }
        }
        if(m_ShopItems.Count <= 0)
        {
            m_ShopItems = ItemService.Instance.GetSelectShopItems(MainService.Instance.UpgradesToProcess[0]);
        }
        for (int i = 0; i < LevelItemPanel.Length; i++)
        {
            LevelItemPanel[i].SetClickAction(OnClickShopItem);
            LevelItemPanel[i].UpdatePanelInfor(m_ShopItems[i].Card, false);
        }
        if (!RunData.ResumedFromState && ServerGameConfig.ins.mGameMode == ServerGameConfig.GameMode.Single)
        {
            SaveManager.Instance.SaveGameData(RunData.ins.GetState(m_ShopItems, new LevelUpCard[] { }, 0, 0, 0, 0, 0));
        }
        RunData.ResumedFromState = false;
    }
    private void SetNewShopDatas()
    {
        m_ShopItems = ItemService.Instance.GetSelectShopItems(MainService.Instance.UpgradesToProcess[0]);
        for (int i = 0; i < LevelItemPanel.Length; i++)
        {
            LevelItemPanel[i].UpdatePanelInfor(m_ShopItems[i].Card, false);
        }
        if (ServerGameConfig.ins.mGameMode == ServerGameConfig.GameMode.Single)
        {
            SaveManager.Instance.SaveGameData(RunData.ins.GetState(m_ShopItems, new LevelUpCard[] { }, 0, 0, 0, 0, 0));
        }
    }
    private void OnClickShopItem(CardBase m_CardBase)
    {
        AudioManager.Instance.PlayBuyOnShot();
        m_ShopItems.Clear();

        if (m_CardBase is PropCard)
        {
            //复制武器道具
            if(m_CardBase.CardNumber == 100171)
            {
                RunData.ins.AddWeapon(RunData.ins.StartingWeapon);
                int slot_count = RunData.ins.Effects.GetEffect<int>("weapon_slot");
                if (RunData.ins.Weapons.Count == slot_count)
                    MissionData_SO.Instance.UpdateMissionProgress(E_MissionName.装满武器, 1, 0);
            }
            else
            {
                RunData.ins.AddItem(m_CardBase);
            }
        }
        else if (m_CardBase is LevelUpCard)
        {
            RunData.ins.AddLevelUpItem(m_CardBase);
        }
        else
        {
            WeaponCard card = (WeaponCard)m_CardBase;
            if (RunData.ins.HasWeaponSlotAvailable(card.AttackType))
            {
                RunData.ins.AddWeapon(card);
            }
        }
        MainService.Instance.UpgradesToProcess.RemoveAt(0);
        SetActivePanel();

        MissionData_SO.Instance.UpdateMissionProgress(E_MissionName.角色提升, 1, 0);
        MissionData_SO.Instance.CheckMissionGameEffect();
    }
    public void OnClickRefresh()
    {
        AdMgrHelper.ShowAD(3, () =>
        {
            SetNewShopDatas();
        });
    }
    public void OnClickGetAll()
    {
        AdMgrHelper.ShowAD(3, () =>
        {
            AddAllShopItem();
        });
    }
    private void AddAllShopItem()
    {
        foreach (var item in m_ShopItems)
        {
            AddShopItem(item.Card);
        }
        AudioManager.Instance.PlayBuyOnShot();
        m_ShopItems.Clear();
        EventHelper.DispatchOnStatUpdated(RunData.ins, "", 0);
        MainService.Instance.UpgradesToProcess.RemoveAt(0);
        SetActivePanel();
        MissionData_SO.Instance.UpdateMissionProgress(E_MissionName.角色提升, 1, 0);
        MissionData_SO.Instance.CheckMissionGameEffect();
    }
    private void AddShopItem(CardBase m_CardBase)
    {
        if (m_CardBase is PropCard)
        {
            //复制武器道具
            if (m_CardBase.CardNumber == 100171)
            {
                RunData.ins.AddWeapon(RunData.ins.StartingWeapon);
                int slot_count = RunData.ins.Effects.GetEffect<int>("weapon_slot");
                if (RunData.ins.Weapons.Count == slot_count)
                    MissionData_SO.Instance.UpdateMissionProgress(E_MissionName.装满武器, 1, 0);
            }
            else
            {
                RunData.ins.AddItem(m_CardBase);
            }
        }
        else if (m_CardBase is LevelUpCard)
        {
            RunData.ins.AddLevelUpItem(m_CardBase);
        }
        else
        {
            WeaponCard card = (WeaponCard)m_CardBase;
            if (RunData.ins.HasWeaponSlotAvailable(card.AttackType))
            {
                RunData.ins.AddWeapon(card);
            }
        }
    }

    public override void UIResponse_Close()
    {
        base.UIResponse_Close();
    }
}
