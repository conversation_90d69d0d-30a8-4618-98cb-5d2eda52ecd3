﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class EnemyProjectileRotating : EnemyProjectile
{
    public int MinTargetDistance = 100;
    public int MaxTargetDistance = 100;
    public int Speed = 100;
    public bool DetroyOnHit = false;
    public float Damage = 1f;
    public float DamageIncreaseEachWave = 1f;

    float m_ActualTargetDistance = 0;

    private void OnEnable()
    {
        SetDamage((int)(Damage + (RunData.GameWave * DamageIncreaseEachWave)));
        m_ActualTargetDistance = Random.Range(MinTargetDistance, MaxTargetDistance);
    }

    public override void OnUpdate(float deltaTime)
    {
        Vector2 pos = CachedTransform.localPosition;
        if (pos.x < m_ActualTargetDistance)
        {
            pos.x += Speed * 1f * deltaTime;
        }
        CachedTransform.localPosition = pos;
    }
    protected override void OnHitBoxHitSomething(Unit thing_hit)
    {
        if (DetroyOnHit)
        {
            SetTobeDisabled();
        }
    }
}
