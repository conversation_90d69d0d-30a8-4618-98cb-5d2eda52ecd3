﻿using DG.Tweening;
using DragonBones;
using System.Collections.Generic;
using UnityEngine;
using Transform = UnityEngine.Transform;

public class ShowPlayer : MonoBehaviour
{
    [Header("Weapon Prefabe")]
    public WeaponBase MeleeWeapon;
    public WeaponBase RangedWeapon;
    public Transform WeaponContainer;

    [Header("Appearence Prefabe")]
    public SpriteRenderer AppeareancePrefabe;
    public Transform AppeareanceParent;
    [<PERSON><PERSON>("Dragon Bones Container")]
    public GameObject BonesContainer;

    WeaponBase m_RangedWeapon;
    WeaponBase m_MeleeWeapon;
    UnityArmatureComponent m_UnityArmature;
    Slot m_Slot;
    Slot m_HandRSlot;
    Bone m_HandRBone;
    List<WeaponCard> m_Cards = null;
    
    // 记录初始偏移量
    private Vector3 m_OffsetFromHand; 
    private Quaternion m_RotationOffsetFromHand;
    private bool m_OffsetInitialized = false;
    
    private Dictionary<string, SpriteRenderer> m_AppeareanceSprites = new Dictionary<string, SpriteRenderer>();

    public void Init()
    {
        m_UnityArmature = ExtensionsStatic.CreateDragonBones(GameData.RoleDragonBonesName, BonesContainer, 25, false, true, "Player");
        m_UnityArmature.animation.Play("Idle");
        m_Slot = m_UnityArmature.armature.GetSlot("Body");
        
        m_HandRSlot = m_UnityArmature.armature.GetSlot("Hand_R");
        if (m_HandRSlot != null)
        {
            m_HandRBone = m_HandRSlot.parent;
            
            // 初始化时计算相对于Hand_R的偏移量
            if (WeaponContainer != null && !m_OffsetInitialized)
            {
                InitializeHandOffset();
            }
        }
        else
        {
            Debug.LogError("无法找到Hand_R骨骼插槽，请检查DragonBones配置！");
        }
    }
    
    private void Start()
    {
        // 启动时计算相对于Hand_R的偏移量
        if (WeaponContainer != null && m_HandRBone != null && !m_OffsetInitialized)
        {
            InitializeHandOffset();
        }
    }
    
    private void InitializeHandOffset()
    {
        // 获取手部骨骼的世界坐标
        Vector3 handPos = GetHandWorldPosition();
        
        // 计算武器容器相对于手部的偏移量
        m_OffsetFromHand = WeaponContainer.position - handPos;
        
        // 计算旋转偏移
        float handRotation = m_HandRBone.global.rotation * Mathf.Rad2Deg;
        Quaternion handWorldRotation = Quaternion.Euler(0, 0, handRotation);
        m_RotationOffsetFromHand = Quaternion.Inverse(handWorldRotation) * WeaponContainer.rotation;
        
        m_OffsetInitialized = true;
    }
    
    private Vector3 GetHandWorldPosition()
    {
        // 获取骨骼的局部坐标
        Vector3 boneLocalPosition = new Vector3(m_HandRBone.global.x, m_HandRBone.global.y, 0);
        
        // 转换为世界坐标
        return m_UnityArmature.transform.TransformPoint(boneLocalPosition);
    }
    
    private void Update()
    {
        UpdateWeaponPosition();
    }
    
    private void UpdateWeaponPosition()
    {
        if (m_HandRBone != null && WeaponContainer != null && m_OffsetInitialized)
        {
            // 获取手部骨骼的当前世界坐标
            Vector3 handWorldPos = GetHandWorldPosition();
            
            // 应用初始偏移量
            WeaponContainer.position = handWorldPos + m_OffsetFromHand;
            
            // 应用旋转
            float handRotation = m_HandRBone.global.rotation * Mathf.Rad2Deg;
            Quaternion handWorldRotation = Quaternion.Euler(0, 0, handRotation);
            WeaponContainer.rotation = handWorldRotation * m_RotationOffsetFromHand;
        }
    }

    public void UpdateSkin(int index)
    {
        m_Slot.displayIndex = index;
    }
    public void ShowAppeareance()
    {
        ClearAppeareances();
        List<AppearanceData> appearances = RunData.ins.AppearanceData;
        for (int i = appearances.Count - 1; i >= 0; i--)
        {
            UpdatePlayerSkin(appearances[i]);
        }
    }
    public void ShowWeapon(List<WeaponCard> weapons)
    {
        m_Cards = weapons;
        if (m_MeleeWeapon == null)
        {
            m_MeleeWeapon = Instantiate(MeleeWeapon, WeaponContainer);
            m_RangedWeapon = Instantiate(RangedWeapon, WeaponContainer);
            
            // 重置局部坐标，确保武器正确显示在武器容器上
            m_MeleeWeapon.transform.localPosition = Vector3.zero;
            m_MeleeWeapon.transform.localRotation = Quaternion.identity;
            m_RangedWeapon.transform.localPosition = Vector3.zero;
            m_RangedWeapon.transform.localRotation = Quaternion.identity;
        }
        if(weapons == null || weapons.Count <= 0)
        {
            m_RangedWeapon.DisableWeapon();
            m_MeleeWeapon.DisableWeapon();
            return;
        }
        for (int i = weapons.Count - 1; i >= 0; i--)
        {
            AddWeapon(weapons[i]);
        }
    }
    protected void AddWeapon(WeaponCard data)
    {        
        WeaponBase weapon;
        if (data.AttackType == AttackType.SHOOT)
        {
            m_MeleeWeapon.DisableWeapon();
            weapon = m_RangedWeapon;
        }
        else
        {
            m_RangedWeapon.DisableWeapon();
            weapon = m_MeleeWeapon;
        }
        weapon.DisableParticle();
        weapon.WeaponEnable(null, data);

        if (Random.value < 0.3f)
        {
            // 重置武器的局部变换，因为它们现在会跟随Hand_R
            weapon.CachedTransform.localEulerAngles = Vector3.zero;
            weapon.CachedTransform.localPosition = Vector3.zero;
            if (data.IsRangedWeapon)
            {
                weapon.CachedTransform.DOKill();
                weapon.CachedTransform.DOLocalRotate(Vector3.forward * 180, 0.08f).SetLoops(4, LoopType.Incremental).SetAutoKill().SetEase(Ease.InCubic);
            }
            else
            {
                weapon.CachedTransform.DOKill();
                if (data.AttackType == AttackType.THRUST)
                {
                    weapon.CachedTransform.DOLocalMoveX(80, 0.08f).SetLoops(4, LoopType.Yoyo).SetAutoKill().SetEase(Ease.Linear);
                }
            }
        }
        m_UnityArmature.animation.Play("Idle");
    }

    protected void UpdatePlayerSkin(AppearanceData data)
    {
        if (m_AppeareanceSprites.TryGetValue(data.Position, out SpriteRenderer sprite))
        {
            sprite.sprite = data.Sprite;
            sprite.sortingOrder = data.Depth;
        }
        else
        {
            SpriteRenderer render = Instantiate(AppeareancePrefabe, AppeareanceParent);
            render.sprite = data.Sprite;
            m_AppeareanceSprites.Add(data.Position, render);
            render.sortingOrder = data.Depth;
        }
    }
    protected void ClearAppeareances()
    {
        foreach (var item in m_AppeareanceSprites.Values)
        {
            item.sprite = null;
        }
    }
    public void PlayAnimation(string name = "")
    {
        DragonBones.Animation animation = m_UnityArmature.animation;
        if (string.IsNullOrEmpty(name))
        {
            name = AniamtionNames.GetRandomElementArray();
        }
        if(name == animation.lastAnimationName)
        {
            return;
        }
        if (name.Contains("Hand") || name.Equals("Wait", System.StringComparison.Ordinal))
        {
            m_MeleeWeapon.DisableWeapon();
            m_RangedWeapon.DisableWeapon();
        }
        else if(m_Cards != null)
        {            
            for (int i = m_Cards.Count - 1; i >= 0; i--)
            {
                if (m_Cards[i].AttackType == AttackType.SHOOT)
                {
                    m_RangedWeapon.gameObject.SetActive(true);
                }
                else
                {
                    m_MeleeWeapon.gameObject.SetActive(true);
                }
            }
        }
        animation.Play(name);
    }
    readonly string[] AniamtionNames = { "Win_02_Hand","Wait","Idle","Walk" };
}
