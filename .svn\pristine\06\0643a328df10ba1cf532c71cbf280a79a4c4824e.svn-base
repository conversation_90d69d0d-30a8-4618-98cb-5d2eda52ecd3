﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class UISFX : MonoBehaviour
{
    [Header("播放器")]
    public AudioSource audioSource;
    [Header("音源")]
    public AudioClip click;
    public AudioClip close;    
    public void PlayrAudio(UISFXType uISFXType,AudioClip clip = null)
    {
        audioSource.pitch = 1f + Random.Range(-0.1f, 0.1f);
        switch (uISFXType)
        {
            case UISFXType.Click:
                audioSource.clip = click;
                break;
            case UISFXType.Close:
                audioSource.clip = close;
                break;
            case UISFXType.Other:
                audioSource.PlayOneShot(clip);
                return;
        }
        audioSource.Play();
    }

    private static UISFX _instance;
    public static UISFX Instance
    {
        get
        {
            if (_instance == null) { _instance = FindObjectOfType<UISFX>(); }
            return _instance;
        }
    }
}
public enum UISFXType
{
    Click,
    Close,
    Other,
}
