﻿using Coffee.UIEffects;
using DG.Tweening;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Transform = UnityEngine.Transform;

namespace STORE
{
    public class GamePass : UIMenuBase
    {
        [Header("SFX")]
        public AudioClip LevelUp;

        [Header("Content Layout")]
        public LayoutGroup ContentLayout;        

        [Header("货币图片")]
        public Sprite LowGoldSprite;
        public Sprite MiddleGoldSprite;
        public Sprite HighGoldSprite;

        public ScrollRect UIScrollRect;

        [Header("货币")]
        public Text DiamondText;
        public Text Currency_2;
        public Text Currency_3;
        [Header("通信证 Item 信息页面")]
        public RectTransform InforPage;
        public PassItemInfor[] PassItemInfors;
        public GameObject HighPassTip;
        public GameObject HightPassBuyTip;
        public Text HighPassPrice;

        [Header("Conversion")]
        public Slider ConversionSlider;
        public Text CountValue;
        public Text NeedValue;
        public Image NeedIcon;
        public Image TargetIcon;
        public GameObject ConversionTip;
        public Image TipIcon;
        public UIEffect ConfirmEffect;

        [Header("Item Prefab")]
        public PassItem FreeItemPrefab;
        public Transform FreeItemContainer;
        public PassItem AdvancedItemPrefab;
        public Transform AdvancedItemContainer;

        int m_MiddleCurrencyCount;
        int m_HighCurrencyCount;

        int m_ConversionCount;
        int m_CurrencyRatio;
        int m_CurrencyType;
        int m_NeedCurrency;

        int m_SelectItemIndex;
        float m_ItemMaxEx;

        List<PassData> m_PassDatas;
        Dictionary<int, PassItemData[]> m_PassItemDic;
        PlayerSave m_Save;

        private void Start()
        {
            InforPage.gameObject.SetActive(true);
            CloseInforPage();
            ConversionSlider.onValueChanged.AddListener(OnSliderValueUpdate);
            m_PassItemDic = new Dictionary<int, PassItemData[]>();
            m_Save = SaveManager.Instance.state;
            m_PassDatas = GameStoreData.Instance.PassData;
            m_SelectItemIndex = -1;
            m_ItemMaxEx = (GameStoreData.Instance.PassMaxEX / m_PassDatas[0].PassItemDatas.Count);
            StartCoroutine(InitPassPage());
            ClientMsgListener.AddListener(ClientListenerType.ChangeDiamond, UpdateDiamond);
        }

        private void OnDisable()
        {
            ConversionSlider.transform.parent.gameObject.SetActive(false);
            CloseInforPage();
        }
        private void OnDestroy()
        {
            ClientMsgListener.RemoveListener(ClientListenerType.ChangeDiamond, UpdateDiamond);
        }
        IEnumerator InitPassPage()
        {
            AdvancedItemPrefab.gameObject.SetActive(false);
            FreeItemPrefab.gameObject.SetActive(false);
            if (m_Save.HighPassIsUnlock)
            {
                HightPassBuyTip.SetActive(false);
                Destroy(HighPassTip);
                Destroy(HighPassPrice.gameObject);
            }
            else
            {
                //价格
                HighPassPrice.text = "Buy";
            }
            WaitForEndOfFrame wait = new WaitForEndOfFrame();
            m_MiddleCurrencyCount = m_Save.MiddleCurrencyNumber;
            m_HighCurrencyCount = m_Save.HighCurrencyNumber;
            DiamondText.text = m_Save.DiamondNumber.GetIntToStringLimitLength(7);
            Currency_2.text = m_MiddleCurrencyCount.ToString();
            Currency_3.text = m_HighCurrencyCount.ToString();

            int passCount = m_PassDatas.Count;
            int passItemCount = m_PassDatas[0].PassItemDatas.Count;
            int level;
            for (int index = 0; index < passItemCount; index++)
            {
                for (int passIndex = 0; passIndex < passCount; passIndex++)
                {
                    bool isFree = m_PassDatas[passIndex].IsFree;
                    PassItemData item = m_PassDatas[passIndex].PassItemDatas[index];
                    level = index + 1;
                    item.IsLock = level > m_Save.PassLevel || (!isFree && !m_Save.HighPassIsUnlock);
                    item.IsWaitingGet = (level > m_Save.UnlockedLevel || item.IsLock) && index < m_Save.PassLevel;
                    if (passIndex == 0)
                    {
                        m_PassItemDic.Add(index, new PassItemData[m_PassDatas.Count]);
                    }
                    InitPassDataItemData(isFree, item, index, passIndex);
                    yield return wait;
                }
            }

            //初始定位            
/*#if Horizontal_Screen
            UIScrollRect.horizontalNormalizedPosition = m_Save.UnlockedLevel/m_PassDatas.Count;
#else
            UIScrollRect.verticalNormalizedPosition = m_Save.UnlockedLevel/m_PassDatas.Count;
#endif*/
            //销毁预制体
            Destroy(AdvancedItemPrefab.gameObject);
            Destroy(FreeItemPrefab.gameObject);
        }
        void InitPassDataItemData(bool free, PassItemData passItemData, int index, int passIndex)
        {
            PassItem item;
            m_PassItemDic[index][passIndex] = passItemData;
            if (free)
            {
                item = Instantiate(FreeItemPrefab, FreeItemContainer);

                //显示Level
                item.Level.text = "LV." + (index + 1);
                //处理进度条
                if (!passItemData.IsLock)
                {
                    item.SliderImage.fillAmount = 1f;
                }
                else
                {
                    if (index == m_Save.PassLevel)
                    {
                        float surplusEx = m_Save.CurrentPassEx - (index - 1) * m_ItemMaxEx;
                        item.SliderImage.fillAmount = surplusEx / m_ItemMaxEx;
                    }
                    else
                    {
                        item.SliderImage.fillAmount = 0;
                    }
                }
            }
            else
            {
                if (passItemData.ItemCardType == PassItemType.Character || passItemData.Icon != null)
                {
                    item = Instantiate(AdvancedItemPrefab, AdvancedItemContainer);
                }
                else
                {
                    GameObject gameObject = Instantiate(AdvancedItemPrefab, AdvancedItemContainer).gameObject;
                    Destroy(gameObject.GetComponent<UIShiny>());
                    Destroy(gameObject.GetComponent<Image>());
                    for (int i = 0; i < gameObject.transform.childCount; i++)
                    {
                        Destroy(gameObject.transform.GetChild(i).gameObject);
                    }
#if Vertical_Screen
                    gameObject.transform.SetSiblingIndex(2);
#endif
                    return;
                }
            }
            passItemData.ItemObject = item;
            item.IninData(passItemData, index, m_Save.UnlockedLevel);
#if Vertical_Screen
            item.transform.SetSiblingIndex(2);
#endif
            item.gameObject.AddComponent<Button>().onClick.AddListener(delegate { OnClickPassItem(index); });                       
            ShowCanUnlockTip(passItemData);
        }

        void OnClickPassItem(int index)
        {
            if (index != m_SelectItemIndex)
            {
                Vector2 pos = InforPage.position;
                for (int i = 0; i < m_PassDatas.Count; i++)
                {
                    PassItemData itemData = m_PassDatas[i].PassItemDatas[index];
                    //如果时待领取物品，优先执行领取
                    if (i == 0 && itemData.IsWaitingGet)
                    {
                        for (int itemIndex = index; itemIndex >= m_Save.UnlockedLevel; itemIndex--)
                        {
                            UnlockPassItem(itemIndex);
                        }
                        UISFX.Instance.PlayrAudio(UISFXType.Other, LevelUp);
                        return;
                    }
                    if (itemData.ItemCardType != PassItemType.Character && itemData.Icon == null)
                    {
                        PassItemInfors[i].gameObject.SetActive(false);
                        continue;
                    }
                    PassItemInfors[i].SetShowData(itemData);
#if Vertical_Screen
                    pos.y = itemData.ItemObject.transform.position.y;
#else
                    pos.x = itemData.ItemObject.transform.position.x;
#endif

                }
                m_SelectItemIndex = index;
                InforPage.position = pos;
                m_LastInforPos = pos;
            }
            else
            {
                UpdateInforPageState();
            }
            UISFX.Instance.PlayrAudio(UISFXType.Click);
        }
        public void OnGetAllUnlockPassItem()
        {
            UISFX.Instance.PlayrAudio(UISFXType.Click);
            for (int i = m_Save.UnlockedLevel; i < m_Save.PassLevel; i++)
            {
                UnlockPassItem(i);
            }
        }
        /// <summary>
        /// 解锁通行证中的指定项
        /// </summary>
        void UnlockPassItem(int index)
        {
            PassItemData[] passItems = m_PassItemDic[index];
            m_Save.UnlockedLevel = Mathf.Max(m_Save.UnlockedLevel, index + 1);
            //清除标识
            for (int i = 0; i < passItems.Length; i++)
            {
                if (!passItems[i].IsWaitingGet ||
                    passItems[i].IsLock ||
                    passItems[i].ItemObject == null
                    ) continue;
                passItems[i].IconTweener.Kill(true);
                passItems[i].GetReward();
            }
            CloseInforPage();            
            SaveManager.Instance.Save();
        }

        /// <summary>
        /// 解锁高级通行证
        /// </summary>
        public void OnClickUnlockHighPass()
        {
            if (m_Save.HighPassIsUnlock) return;
            PaySDKManager.ins.StartPay("PD01", (PayResult purchase) =>
            {
                Debug.Log("购买成功:" + purchase.Type);
                if(purchase.Type == PayResult.PayResultType.Consume)
                {
                    SetHightPassUnlock();
                }
            },
            (string purchaseToken) =>
            {
                Debug.Log("购买失败");
            });            
        }
        void SetHightPassUnlock()
        {
            m_Save.HighPassIsUnlock = true;

            //奖励1
            m_Save.PassLevel += 5;
            m_Save.CurrentPassEx = (int)(m_ItemMaxEx * m_Save.PassLevel);
            SaveManager.Instance.Save();
            //自动领取所有可领取物体            
            foreach (var passDataItem in m_PassDatas)
            {
                for (int i = 0; i < m_Save.PassLevel; i++)
                {
                    PassItemData itemData = passDataItem.PassItemDatas[i];
                    if (!itemData.IsLock) continue;

                    //处理进度条
                    if (itemData.ItemObject.SliderImage)
                    {
                        itemData.ItemObject.SliderImage.fillAmount = 1f;
                    }

                    //打开锁定开关
                    itemData.IsLock = false;
                    itemData.IsWaitingGet = true;
                    if (itemData.ItemObject)
                    {
                        AddUIDissolve(itemData.ItemObject.transform.GetChild(1).gameObject, 0.6f).Play();
                    }
                    UnlockPassItem(i);
                }
            }

            //奖励2
            PlayerPrefs.SetInt("ForverNOAD", 1);

            //更新UI
            HightPassBuyTip.SetActive(false);
            Destroy(HighPassTip);
            Destroy(HighPassPrice.gameObject);
        }


        void OnSliderValueUpdate(float value)
        {
            m_ConversionCount = (int)value;
            CountValue.text = value.ToString();
            if (value * m_CurrencyRatio > m_NeedCurrency)
            {
                CountValue.color = Color.red;
                ConfirmEffect.effectMode = EffectMode.Grayscale;
            }
            else
            {
                CountValue.color = Color.white;
                ConfirmEffect.effectMode = EffectMode.None;
            }
        }
        public void ShowConversionPage(int currencyType)
        {
            if (currencyType == 2)
            {
                m_CurrencyRatio = GameStoreData.Instance.LowToMiddleCurrencyRatio;
                NeedIcon.sprite = LowGoldSprite;
                TargetIcon.sprite = MiddleGoldSprite;
                m_NeedCurrency = m_Save.DiamondNumber;
            }
            else
            {
                m_CurrencyRatio = GameStoreData.Instance.MiddleToHighCurrencyRatio;
                NeedIcon.sprite = MiddleGoldSprite;
                TargetIcon.sprite = HighGoldSprite;
                m_NeedCurrency = m_MiddleCurrencyCount;
            }
            m_CurrencyType = currencyType;
            NeedValue.text = string.Format("x {0}", m_CurrencyRatio);
            SetSliderValueMinMax(false);
        }
        public void UpdateSliderValue(bool add)
        {
            if (add)
            {
                ConversionSlider.value += 1;
            }
            else
            {
                ConversionSlider.value -= 1;
            }
        }
        public void SetSliderValueMinMax(bool min)
        {
            if (min)
            {
                ConversionSlider.value = ConversionSlider.minValue;
            }
            else
            {
                int value = m_NeedCurrency / m_CurrencyRatio;
                ConversionSlider.value = value;
            }
        }

        public void OnClickConversion()
        {
            int need = m_ConversionCount * m_CurrencyRatio;
            if (need > m_NeedCurrency)
            {
                ShowTip();
                return;
            }
            m_NeedCurrency -= need;
            if (m_CurrencyType == 2)
            {
                m_Save.DiamondNumber = m_NeedCurrency;
                m_MiddleCurrencyCount += m_ConversionCount;
            }
            else
            {
                m_MiddleCurrencyCount = m_NeedCurrency;
                m_HighCurrencyCount += m_ConversionCount;
            }
            if (m_ConversionCount * m_CurrencyRatio > m_NeedCurrency)
            {
                CountValue.color = Color.red;
                ConfirmEffect.effectMode = EffectMode.Grayscale;
            }
            else
            {
                CountValue.color = Color.white;
                ConfirmEffect.effectMode = EffectMode.None;
            }
            //UpdateCurrency();
        }
        /// <summary>
        /// 更新货币
        /// </summary>
        public void UpdateCurrency()
        {
            m_Save.MiddleCurrencyNumber = m_MiddleCurrencyCount;
            m_Save.HighCurrencyNumber = m_HighCurrencyCount;

            Currency_2.text = m_MiddleCurrencyCount.ToString();
            Currency_3.text = m_HighCurrencyCount.ToString();

            //CurrentSettings.Instance.UpdatePassCurrency(0);
        }
        void UpdateDiamond(object value)
        {
            DiamondText.text = m_Save.DiamondNumber.GetIntToStringLimitLength(7);
        }
        void ShowCanUnlockTip(PassItemData passItemData)
        {
            if (passItemData.IsWaitingGet)
            {
                UIShadow uiShadow = passItemData.ItemObject.UIShadow;
                uiShadow.blurFactor = 1f;
                passItemData.IconTweener = DOTween.To(() => uiShadow.effectDistance = Vector2.zero, x => uiShadow.effectDistance = x, Vector2.one * 5, 1f)
                    .SetLoops(-1, LoopType.Yoyo).OnKill(() =>
                    {
                        uiShadow.effectDistance = Vector2.zero;
                        uiShadow.blurFactor = 0f;
                    });
                if (!passItemData.IsLock)
                {
                    AddUIDissolve(passItemData.ItemObject.transform.GetChild(1).gameObject, 0.6f).Play();
                }
            }
        }
        UIDissolve AddUIDissolve(GameObject gameObject,float playDelay)
        {
            UIDissolve uIDissolve = gameObject.AddComponent<UIDissolve>();
            uIDissolve.effectFactor = 0;
            uIDissolve.softness = 0.85f;
            uIDissolve.color = new Color32(0, 160, 255, 255);
            uIDissolve.colorMode = ColorMode.Multiply;
            uIDissolve.transitionTexture = null;
            uIDissolve.effectPlayer.duration = 1f;
            uIDissolve.effectPlayer.initialPlayDelay = playDelay;
            return uIDissolve;
        }
        void ShowTip()
        {
            TipIcon.sprite = NeedIcon.sprite;
            ConversionTip.SetActive(true);
        }
        void UpdateInforPageState()
        {
#if Vertical_Screen
            bool show = InforPage.anchoredPosition.y != 10000;
#else
            bool show = InforPage.anchoredPosition.x != 10000;
#endif
            if (show)
            {
                CloseInforPage();
            }
            else
            {
                InforPage.position = m_LastInforPos;
            }
        }
        Vector2 m_LastInforPos;
        public void CloseInforPage()
        {
#if Vertical_Screen
            InforPage.anchoredPosition = Vector2.up * 10000;
#else
            InforPage.anchoredPosition = Vector2.right * 10000;
#endif
        }
    }
}
