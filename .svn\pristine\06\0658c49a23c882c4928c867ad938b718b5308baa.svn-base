%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8d0752448a0cf7a46bae8a5f791df382, type: 3}
  m_Name: "Weapon_Plank\u2161"
  m_EditorClassIdentifier: 
  ID: 46
  CardNumber: 10051
  CardTypes: 110000000e000000
  ItemName: Txt_Weapon_Name_Plank
  ItemType: Txt_Type_Explosive,Txt_Stats_Name_Elemental
  IconPath: Image\UI\Weapon\Weapon_10050
  PropertyDescribe: Txt_Weapon_Desc_Explode30
  Effects:
  - {fileID: 11400000, guid: dbb814cfefb9ca64cb44ca63cceee330, type: 2}
  UpgradeIds: 
  UpgradeDatas: []
  Remarks: 
  rarity: 1
  starLevel: 0
  Cost: 0
  EquipType: 0
  EquipLevel: 2
  UpgradeValues:
  - 1.5
  - 3
  - 1.5
  - 3
  - 1.5
  - 3
  - 1.5
  - 3
  - 1.5
  - 3
  - 1.5
  - 3
  - 1.5
  - 3
  - 1.5
  - 3
  - 1.5
  - 3
  - 1.5
  - 3
  AttackType: 2
  InGameSpritePath: Sprite\Weapon\Weapon_10050
  BaseWeaponPrice: 31
  WeaponDefaultLock: 1
  Stats: {fileID: 11400000, guid: c56f198a47b0624428ac0629c3b2c0b4, type: 2}
  HigherLevelCard: {fileID: 11400000, guid: 9550aaf50dc308a43abd4ab78c91c034, type: 2}
  RecommendWeapons:
  - 10054
  RecommendProps:
  - 100135
  - 100153
  - 100011
  - 100017
  - 100039
  - 100044
  - 100157
  - 100160
  - 100085
  - 100107
  - 100123
  - 100048
  - 100052
  - 100105
  - 100023
  - 100030
  - 100031
  - 100049
  - 100054
  - 100058
  - 100133
  - 100151
  - 100043
  - 100057
  - 100093
  - 100115
  - 100140
  - 100150
  RecommendLevelUps:
  - 107
  - 207
  - 307
  - 407
  - 204
  - 304
  - 404
  - 408
  weaponType: 0
  damageType: 0
