using UnityEngine;
using UnityEngine.EventSystems;

public enum SlotType { Backpack, Equipment, Shop, Trash } // 定义槽位类型
public enum ItemCategory
{
    Weapon,      // 武器类（对应搜索结果[1][2]中的SS武器）
    Follower,    // 随从类
}

public class ItemSlotUI : Mono<PERSON>ehavi<PERSON>, IDropHandler
{

    public SlotType slotType = SlotType.Backpack;
    public ItemCategory itemCategory = ItemCategory.Weapon;

    public int slotIndex = -1; 

    public DraggableItemUI currentItemUI { get; private set; } // 当前槽位中的物品UI

    public void OnDrop(PointerEventData eventData)
    {
        Debug.Log($"Item dropped on {gameObject.name} of type {slotType}");
        GameObject droppedObject = eventData.pointerDrag;
        if (droppedObject == null) return;

        DraggableItemUI draggedItem = droppedObject.GetComponent<DraggableItemUI>();
        if (draggedItem == null || draggedItem.weaponInstance == null) return;
        HandleDroppedItem(draggedItem);
        //menuShop.RefreshUI(); // 假设你有这样一个管理器
    }

    private void HandleDroppedItem(DraggableItemUI draggedItem)
    {
        InventoryItem weaponInstance = draggedItem.weaponInstance;
        ItemSlotUI sourceSlot = draggedItem.originalSlot;
        bool operationSuccess = false;

        switch (slotType)
        {
            case SlotType.Backpack:
                if (sourceSlot.slotType == SlotType.Backpack && sourceSlot != this)
                {
                    if (currentItemUI == null)
                    {
                        //RunData.ins.SetBagWeapon(sourceSlot.currentItemUI.weaponInstance, slotIndex);
                        // 目标槽位为空，直接放置
                        PlaceItemInSlot(draggedItem);
                        sourceSlot.ClearSlot();
                        operationSuccess = true;
                    }
                    else
                    {
                        //RunData.ins.SetBagWeapon(sourceSlot.currentItemUI.weaponInstance, slotIndex);
                        // 目标槽位不为空，交换两个槽位的物品
                        DraggableItemUI targetItem = currentItemUI;
                        sourceSlot.PlaceItemInSlot(targetItem);
                        PlaceItemInSlot(draggedItem);
                        operationSuccess = true;
                    }
                }
                else if (sourceSlot.slotType == SlotType.Equipment)
                {
                    // 从装备槽拖到背包
                    operationSuccess = RunData.ins.UnequipItem(weaponInstance,this.slotIndex,itemCategory);
                    if (operationSuccess)
                    {
                        if (currentItemUI == null)
                        {
                            PlaceItemInSlot(draggedItem);
                            sourceSlot.ClearSlot();
                        }
                        else
                        {
                            // 背包槽不为空，交换
                            DraggableItemUI targetItem = currentItemUI;
                            //RunData.ins.EquipCombineWeapon(targetItem.weaponInstance,slotIndex);
                            sourceSlot.PlaceItemInSlot(targetItem);
                            PlaceItemInSlot(draggedItem);
                        }
                    }
                    UIGameShop.Instance.RemoveEquip(weaponInstance.card);
                }
                break;

            case SlotType.Equipment:
                if (sourceSlot.slotType == SlotType.Backpack)
                {
                    // 从背包拖到装备槽
                    operationSuccess = RunData.ins.EquipItem(weaponInstance,this.slotIndex,itemCategory);
                    if (operationSuccess)
                    {
                        if (currentItemUI == null)
                        {
                            PlaceItemInSlot(draggedItem);
                            sourceSlot.ClearSlot();
                        }
                        else
                        {
                            // 装备槽不为空，交换
                            DraggableItemUI targetItem = currentItemUI;
                            //RunData.ins.UnequipCombineWeapon(targetItem.weaponInstance,slotIndex);
                            sourceSlot.PlaceItemInSlot(targetItem);
                            PlaceItemInSlot(draggedItem);
                        }
                    }
                    UIGameShop.Instance.EquipWeapon(weaponInstance.card);
                }
                else if (sourceSlot.slotType == SlotType.Equipment && sourceSlot != this)
                {
                    // 装备槽间移动
                    operationSuccess = true;
                    if (currentItemUI == null)
                    {
                        //RunData.ins.SetEquipWeapon(sourceSlot.currentItemUI.weaponInstance, slotIndex);
                        PlaceItemInSlot(draggedItem);
                        sourceSlot.ClearSlot();
                    }
                    else
                    {
                        //RunData.ins.SetEquipWeapon(sourceSlot.currentItemUI.weaponInstance, slotIndex);
                        DraggableItemUI targetItem = currentItemUI;
                        sourceSlot.PlaceItemInSlot(targetItem);
                        PlaceItemInSlot(draggedItem);
                    }
                }
                break;


        }

    }
    public void UpdateSlotUI()
    {
        if (currentItemUI != null)
        {
            currentItemUI.Initialize(currentItemUI.weaponInstance, this);
        }
        else
        {
            ClearSlot();
        }
    }

    // 将一个DraggableItemUI放置到这个槽位中 (UI层面)
    public void PlaceItemInSlot(DraggableItemUI itemUI)//武器
    {
        if (itemUI == null)
        {
            ClearSlot();
            return;
        }

        RunData.ins.SetItemPlace(itemUI.weaponInstance, slotType, slotIndex,itemCategory);
        itemUI.transform.SetParent(transform); // 成为此槽的子对象
        itemUI.transform.localPosition = Vector3.zero; // 居中或对齐到槽的预设位置
        itemUI.originalSlot = this; // 更新物品UI记录的当前槽位
        currentItemUI = itemUI;
        gameObject.name = $"Slot_Holding_{itemUI.weaponInstance.card.CardNumber}"; // For debugging
        UpdateSlotUI();
    }

    // 清空槽位 (UI层面)
    public void ClearSlot()
    {
        if (currentItemUI != null)
        {
            Destroy(currentItemUI.gameObject);
        }
        currentItemUI = null;
        gameObject.name = $"Slot_Empty_{slotType}"; // For debugging
    }

    // 内部清理引用，不销毁子对象，因为子对象可能被移动到别处了
    private void ClearSlotInternally()
    {
        currentItemUI = null;
        gameObject.name = $"Slot_Empty_{slotType}";
    }
}